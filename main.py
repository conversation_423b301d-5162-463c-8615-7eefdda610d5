import os
from docx import Document
from docx.opc.exceptions import PackageNotFoundError
from docx.oxml.ns import qn
from docx.shared import Inches

def find_section_start(doc, section_start_text):
    """Знайти параграф із вказаним заголовком і повернути його індекс."""
    for i, para in enumerate(doc.paragraphs):
        if section_start_text in para.text:
            return i
    return -1

def copy_content_from_source(source_doc, start_index):
    """Скопіювати вміст із source_doc, починаючи з start_index, включаючи зображення."""
    content = []
    images = []

    for para in source_doc.paragraphs[start_index:]:
        content.append(para._element)

        for run in para.runs:
            for inline_shape in run._element.findall(".//" + qn("w:drawing")):
                blip = inline_shape.find(".//" + qn("a:blip"))
                if blip is not None:
                    rId = blip.get(qn("r:embed"))
                    if rId:
                        image_part = source_doc.part.related_parts.get(rId)
                        if image_part:
                            images.append(image_part.blob)

    return content, images

def main():
    folder_path = r"C:\Users\<USER>\Desktop\комп – копія (3)"
    source_file_path = r"C:\Users\<USER>\Desktop\doc.docx"
    source_section_start_text = "ІНФОРМАЦІЯ ПРО СИСТЕМУ ФАХОВОЇ ПЕРЕДВИЩОЇ ОСВІТИ"

    try:
        source_doc = Document(source_file_path)
        source_start_index = find_section_start(source_doc, source_section_start_text)
        if source_start_index == -1:
            print(f"Помилка: Заголовок '{source_section_start_text}' не знайдено в {source_file_path}")
            return

        content_to_copy, images = copy_content_from_source(source_doc, source_start_index)

        for filename in os.listdir(folder_path):
            if filename.endswith(".docx") and filename != "doc.docx":
                file_path = os.path.join(folder_path, filename)
                try:
                    target_doc = Document(file_path)
                    target_start_index = find_section_start(target_doc, source_section_start_text)

                    if target_start_index == -1:
                        print(f"Помилка: Заголовок '{source_section_start_text}' не знайдено в {filename}")
                        continue

                    # Видаляємо всі параграфи починаючи з target_start_index
                    while len(target_doc.paragraphs) > target_start_index:
                        target_doc.paragraphs[-1]._element.getparent().remove(target_doc.paragraphs[-1]._element)

                    for element in content_to_copy:
                        target_doc._body._element.append(element)

                    for image_data in images:
                        paragraph = target_doc.add_paragraph()
                        run = paragraph.add_run()
                        run.add_picture(image_data, width=Inches(3.0))

                    target_doc.save(file_path)
                    print(f"Оброблено файл: {filename}")

                except PackageNotFoundError:
                    print(f"Помилка: Файл {filename} пошкоджено або недоступний")
                except Exception as e:
                    print(f"Помилка при обробці {filename}: {str(e)}")

    except PackageNotFoundError:
        print(f"Помилка: Файл {source_file_path} пошкоджено або недоступний")
    except Exception as e:
        print(f"Помилка: {str(e)}")

    print("Обробка завершена!")

if name == "main":
    main()
import os
from docx import Document
from docx.opc.exceptions import PackageNotFoundError
from docx.oxml.ns import qn
from docx.shared import Inches

def find_section_start(doc, section_start_text):
    """Знайти параграф із вказаним заголовком і повернути його індекс."""
    for i, para in enumerate(doc.paragraphs):
        if section_start_text in para.text:
            return i
    return -1

def copy_content_from_source(source_doc, start_index):
    """Скопіювати вміст із source_doc, починаючи з start_index, включаючи зображення."""
    content = []
    images = []
    Моя Малявочка, [7/16/25 9:09 PM]
import os
from docx import Document
from docx.opc.exceptions import PackageNotFoundError
from docx.oxml.ns import qn
from docx.shared import Inches

def find_section_start(doc, section_start_text):
    """Знайти параграф із вказаним заголовком і повернути його індекс."""
    for i, para in enumerate(doc.paragraphs):
        if section_start_text in para.text:
            return i
    return -1

def copy_content_from_source(source_doc, start_index):
    """Скопіювати вміст із source_doc, починаючи з start_index, включаючи зображення."""
    content = []
    images = []

    for para in source_doc.paragraphs[start_index:]:
        content.append(para._element)

        for run in para.runs:
            for inline_shape in run._element.findall(".//" + qn("w:drawing")):
                blip = inline_shape.find(".//" + qn("a:blip"))
                if blip is not None:
                    rId = blip.get(qn("r:embed"))
                    if rId:
                        image_part = source_doc.part.related_parts.get(rId)
                        if image_part:
                            images.append(image_part.blob)

    return content, images

def main():
    folder_path = r"C:\Users\<USER>\Desktop\комп – копія (3)"
    source_file_path = r"C:\Users\<USER>\Desktop\doc.docx"
    source_section_start_text = "ІНФОРМАЦІЯ ПРО СИСТЕМУ ФАХОВОЇ ПЕРЕДВИЩОЇ ОСВІТИ"

    try:
        source_doc = Document(source_file_path)
        source_start_index = find_section_start(source_doc, source_section_start_text)
        if source_start_index == -1:
            print(f"Помилка: Заголовок '{source_section_start_text}' не знайдено в {source_file_path}")
            return

        content_to_copy, images = copy_content_from_source(source_doc, source_start_index)

        for filename in os.listdir(folder_path):
            if filename.endswith(".docx") and filename != "doc.docx":
                file_path = os.path.join(folder_path, filename)
                try:
                    target_doc = Document(file_path)
                    target_start_index = find_section_start(target_doc, source_section_start_text)

                    if target_start_index == -1:
                        print(f"Помилка: Заголовок '{source_section_start_text}' не знайдено в {filename}")
                        continue

                    # Видаляємо всі параграфи починаючи з target_start_index
                    while len(target_doc.paragraphs) > target_start_index:
                        target_doc.paragraphs[-1]._element.getparent().remove(target_doc.paragraphs[-1]._element)

                    for element in content_to_copy:
                        target_doc._body._element.append(element)

                    for image_data in images:
                        paragraph = target_doc.add_paragraph()
                        run = paragraph.add_run()
                        run.add_picture(image_data, width=Inches(3.0))

                    target_doc.save(file_path)
                    print(f"Оброблено файл: {filename}")

                except PackageNotFoundError:
                    print(f"Помилка: Файл {filename} пошкоджено або недоступний")
                except Exception as e:
                    print(f"Помилка при обробці {filename}: {str(e)}")

    except PackageNotFoundError:
        print(f"Помилка: Файл {source_file_path} пошкоджено або недоступний")
    except Exception as e:
        print(f"Помилка: {str(e)}")

    print("Обробка завершена!")

if name == "main":
    main()
import os
from docx import Document
from docx.opc.exceptions import PackageNotFoundError
from docx.oxml.ns import qn
from docx.shared import Inches

def find_section_start(doc, section_start_text):
    """Знайти параграф із вказаним заголовком і повернути його індекс."""
    for i, para in enumerate(doc.paragraphs):
        if section_start_text in para.text:
            return i
    return -1

def copy_content_from_source(source_doc, start_index):
    """Скопіювати вміст із source_doc, починаючи з start_index, включаючи зображення."""
    content = []
    images = []

Моя Малявочка, [7/16/25 9:09 PM]
for para in source_doc.paragraphs[start_index:]:
        content.append(para._element)

        for run in para.runs:
            for inline_shape in run._element.findall(".//" + qn("w:drawing")):
                blip = inline_shape.find(".//" + qn("a:blip"))
                if blip is not None:
                    rId = blip.get(qn("r:embed"))
                    if rId:
                        image_part = source_doc.part.related_parts.get(rId)
                        if image_part:
                            images.append(image_part.blob)

    return content, images

def main():
    folder_path = r"C:\Users\<USER>\Desktop\комп – копія (3)"
    source_file_path = r"C:\Users\<USER>\Desktop\doc.docx"
    source_section_start_text = "ІНФОРМАЦІЯ ПРО СИСТЕМУ ФАХОВОЇ ПЕРЕДВИЩОЇ ОСВІТИ"

    try:
        source_doc = Document(source_file_path)
        source_start_index = find_section_start(source_doc, source_section_start_text)
        if source_start_index == -1:
            print(f"Помилка: Заголовок '{source_section_start_text}' не знайдено в {source_file_path}")
            return

        content_to_copy, images = copy_content_from_source(source_doc, source_start_index)

        for filename in os.listdir(folder_path):
            if filename.endswith(".docx") and filename != "doc.docx":
                file_path = os.path.join(folder_path, filename)
                try:
                    target_doc = Document(file_path)
                    target_start_index = find_section_start(target_doc, source_section_start_text)

                    if target_start_index == -1:
                        print(f"Помилка: Заголовок '{source_section_start_text}' не знайдено в {filename}")
                        continue

                    # Видаляємо всі параграфи починаючи з target_start_index
                    while len(target_doc.paragraphs) > target_start_index:
                        target_doc.paragraphs[-1]._element.getparent().remove(target_doc.paragraphs[-1]._element)

                    for element in content_to_copy:
                        target_doc._body._element.append(element)

                    for image_data in images:
                        paragraph = target_doc.add_paragraph()
                        run = paragraph.add_run()
                        run.add_picture(image_data, width=Inches(3.0))

                    target_doc.save(file_path)
                    print(f"Оброблено файл: {filename}")

                except PackageNotFoundError:
                    print(f"Помилка: Файл {filename} пошкоджено або недоступний")
                except Exception as e:
                    print(f"Помилка при обробці {filename}: {str(e)}")

    except PackageNotFoundError:
        print(f"Помилка: Файл {source_file_path} пошкоджено або недоступний")
    except Exception as e:
        print(f"Помилка: {str(e)}")

    print("Обробка завершена!")

if name == "main":
    main()

